import { useTranslations } from 'next-intl';
import Link from 'next/link';

export default function CancelPage() {
  const t = useTranslations('payment');

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
        {/* Error Icon */}
        <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full mb-6">
          <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>

        {/* Error Message */}
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          {t('failed')}
        </h1>
        
        <p className="text-gray-600 dark:text-gray-300 mb-8">
          支付过程中出现了问题，或者您取消了支付。请不要担心，没有任何费用被扣除。
        </p>

        {/* Sad Emoji */}
        <div className="mb-8">
          <div className="text-4xl">😔</div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Link
            href="/"
            className="block w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            {t('retry')}
          </Link>
          
          <Link
            href="/"
            className="block w-full py-3 px-6 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            返回首页
          </Link>
        </div>

        {/* Help Info */}
        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            如果您遇到技术问题，请联系我们的支持团队。
          </p>
        </div>
      </div>
    </div>
  );
}
