# 帮助请求页面 - Help Request Page

这是一个使用 Next.js 构建的简洁帮助请求页面，用于向社区寻求支持和帮助。

## 功能特点

- 🎨 简洁美观的设计
- 📱 完全响应式布局
- 🌙 支持深色模式
- ⚡ 基于 Next.js 15 和 React 19
- 🎯 使用 Tailwind CSS 进行样式设计
- 🚀 优化的 Vercel 部署配置

## 快速开始

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看页面。

## 自定义内容

你可以通过编辑以下文件来自定义页面内容：

- `app/page.tsx` - 主页面内容
- `app/layout.tsx` - 页面标题和元数据
- `app/globals.css` - 全局样式

### 更新联系信息

在 `app/page.tsx` 中找到以下部分并更新为你的联系信息：

```tsx
// 邮箱链接
href="mailto:<EMAIL>"

// GitHub 链接
href="https://github.com/yourusername"

// 微信和QQ
微信: your_wechat_id
QQ: 123456789
```

## 部署到 Vercel

1. 将代码推送到 GitHub 仓库
2. 在 [Vercel](https://vercel.com) 上导入你的仓库
3. Vercel 会自动检测 Next.js 项目并进行部署

或者使用 Vercel CLI：

```bash
npm i -g vercel
vercel
```

## 技术栈

- **框架**: Next.js 15
- **UI 库**: React 19
- **样式**: Tailwind CSS 4
- **字体**: Geist Sans & Geist Mono
- **部署**: Vercel

## 许可证

MIT License
