import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { headers } from 'next/headers';

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = headers().get('stripe-signature');

  if (!signature) {
    return NextResponse.json(
      { error: 'Missing stripe-signature header' },
      { status: 400 }
    );
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    );
  }

  // 处理事件
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object;
      console.log('Payment successful:', session.id);
      
      // 这里可以添加数据库记录、发送邮件等逻辑
      // await recordDonation(session);
      // await sendThankYouEmail(session.customer_email);
      
      break;
    
    case 'invoice.payment_succeeded':
      // 处理订阅支付成功
      const invoice = event.data.object;
      console.log('Subscription payment successful:', invoice.id);
      break;
    
    case 'invoice.payment_failed':
      // 处理订阅支付失败
      const failedInvoice = event.data.object;
      console.log('Subscription payment failed:', failedInvoice.id);
      break;
    
    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  return NextResponse.json({ received: true });
}
