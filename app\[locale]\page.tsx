import {useTranslations} from 'next-intl';
import DonationForm from '@/components/DonationForm';
import ProgressBar from '@/components/ProgressBar';

export default function Home() {
  const t = useTranslations('donation');
  const tContact = useTranslations('contact');
  const tFooter = useTranslations('footer');

  // 模拟数据 - 实际项目中应该从数据库获取
  const donationData = {
    goal: 50000,
    raised: 23500,
    supporters: 156
  };

  const progressPercentage = (donationData.raised / donationData.goal) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full mb-6">
              <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
              {t('subtitle')}
            </p>
          </div>

          {/* Progress Section */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 md:p-12 mb-12">
            <div className="grid md:grid-cols-3 gap-8 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {t('currency')}{donationData.goal.toLocaleString()}
                </div>
                <div className="text-gray-600 dark:text-gray-400">{t('goal')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                  {t('currency')}{donationData.raised.toLocaleString()}
                </div>
                <div className="text-gray-600 dark:text-gray-400">{t('raised')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                  {donationData.supporters}
                </div>
                <div className="text-gray-600 dark:text-gray-400">{t('supporters')}</div>
              </div>
            </div>
            
            <ProgressBar percentage={progressPercentage} />
          </div>

          {/* Description Section */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 md:p-12 mb-12">
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-8">
              {t('description')}
            </p>
            
            <h2 className="text-2xl md:text-3xl font-semibold text-gray-900 dark:text-white mb-6">
              {t('whyDonate')}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-3 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700 dark:text-gray-300">{t('reasons.server')}</span>
              </div>
              <div className="flex items-start">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-3 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700 dark:text-gray-300">{t('reasons.development')}</span>
              </div>
              <div className="flex items-start">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-3 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700 dark:text-gray-300">{t('reasons.support')}</span>
              </div>
              <div className="flex items-start">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-3 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700 dark:text-gray-300">{t('reasons.features')}</span>
              </div>
            </div>
          </div>

          {/* Donation Form */}
          <DonationForm />

          {/* Contact Section */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 md:p-12 mt-12">
            <h2 className="text-2xl md:text-3xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
              {tContact('title')}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center justify-center p-6 bg-blue-50 dark:bg-blue-900/20 rounded-xl hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group"
              >
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span className="text-blue-600 dark:text-blue-400 font-medium group-hover:underline">
                  {tContact('email')}
                </span>
              </a>
              
              <a
                href="https://github.com/yourusername"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center p-6 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group"
              >
                <svg className="w-6 h-6 text-gray-700 dark:text-gray-300 mr-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                <span className="text-gray-700 dark:text-gray-300 font-medium group-hover:underline">
                  {tContact('github')}
                </span>
              </a>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-12">
            <p className="text-gray-500 dark:text-gray-400 mb-2">
              {tFooter('thanks')}
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500">
              {tFooter('secure')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
