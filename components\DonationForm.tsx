'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';

export default function DonationForm() {
  const t = useTranslations('donation');
  const locale = useLocale();
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState('');
  const [donationType, setDonationType] = useState<'one-time' | 'monthly'>('one-time');
  const [isProcessing, setIsProcessing] = useState(false);

  // 预设金额根据语言调整
  const presetAmounts = locale === 'zh' 
    ? [50, 100, 200, 500, 1000] 
    : [10, 25, 50, 100, 200];

  const currency = locale === 'zh' ? '¥' : '$';

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    setSelectedAmount(null);
  };

  const handleDonate = async () => {
    const amount = selectedAmount || parseFloat(customAmount);
    if (!amount || amount <= 0) return;

    setIsProcessing(true);
    
    try {
      // 这里将集成实际的支付处理
      console.log('Processing donation:', { amount, type: donationType, locale });
      
      // 模拟支付处理
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 实际项目中这里会调用支付 API
      alert(`Thank you for your ${currency}${amount} donation!`);
    } catch (error) {
      console.error('Payment error:', error);
      alert('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const finalAmount = selectedAmount || parseFloat(customAmount) || 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 md:p-12">
      <h2 className="text-2xl md:text-3xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
        {t('selectAmount')}
      </h2>

      {/* Donation Type Toggle */}
      <div className="flex justify-center mb-8">
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-1 flex">
          <button
            onClick={() => setDonationType('one-time')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              donationType === 'one-time'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {t('oneTimeDonation')}
          </button>
          <button
            onClick={() => setDonationType('monthly')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              donationType === 'monthly'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {t('monthlyDonation')}
          </button>
        </div>
      </div>

      {/* Preset Amounts */}
      <div className="grid grid-cols-3 md:grid-cols-5 gap-4 mb-8">
        {presetAmounts.map((amount) => (
          <button
            key={amount}
            onClick={() => handleAmountSelect(amount)}
            className={`p-4 rounded-xl border-2 transition-all hover:scale-105 ${
              selectedAmount === amount
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
            }`}
          >
            <div className="text-lg font-semibold">
              {currency}{amount}
            </div>
          </button>
        ))}
      </div>

      {/* Custom Amount */}
      <div className="mb-8">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('customAmount')}
        </label>
        <div className="relative">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
            {currency}
          </span>
          <input
            type="number"
            value={customAmount}
            onChange={(e) => handleCustomAmountChange(e.target.value)}
            placeholder="0"
            className="w-full pl-8 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            min="1"
          />
        </div>
      </div>

      {/* Donate Button */}
      <button
        onClick={handleDonate}
        disabled={finalAmount <= 0 || isProcessing}
        className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all ${
          finalAmount > 0 && !isProcessing
            ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
            : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
        }`}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </div>
        ) : (
          `${t('donate')} ${finalAmount > 0 ? `${currency}${finalAmount}` : ''}`
        )}
      </button>

      {/* Security Notice */}
      <div className="mt-6 flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
        Secure payment powered by Stripe
      </div>
    </div>
  );
}
